import { test, expect } from '@playwright/test';

test.describe('认证功能测试', () => {
  const testUser = {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User'
  };

  const adminUser = {
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin User'
  };

  test.beforeEach(async ({ page }) => {
    // 设置默认超时时间
    page.setDefaultTimeout(15000);

    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Console error:', msg.text());
      }
    });
  });

  test('访问登录页面', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // 点击头部的登录按钮
    const signInButton = page.locator('header').getByRole('button', { name: '登录' }).or(
      page.locator('header').getByRole('button', { name: 'Sign In' })
    );
    await expect(signInButton).toBeVisible();
    await signInButton.click();

    // 等待登录对话框出现
    await page.waitForTimeout(1000);

    // 验证登录表单显示（支持中英文）
    const welcomeText = page.getByText('欢迎回来').or(page.getByText('Welcome Back'));
    await expect(welcomeText).toBeVisible();

    const emailField = page.getByLabel('邮箱地址').or(page.getByLabel('Email Address'));
    await expect(emailField).toBeVisible();

    const passwordField = page.getByLabel('密码').or(page.getByLabel('Password'));
    await expect(passwordField).toBeVisible();

    // 查找登录提交按钮
    const loginSubmitButton = page.getByRole('button', { name: '登录' }).or(
      page.getByRole('button', { name: 'Sign In' })
    ).filter({ hasNotText: '登录以保存收藏' });
    await expect(loginSubmitButton).toBeVisible();
  });

  test('切换到注册页面', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // 点击头部的登录按钮
    const signInButton = page.locator('header').getByRole('button', { name: '登录' }).or(
      page.locator('header').getByRole('button', { name: 'Sign In' })
    );
    await signInButton.click();
    await page.waitForTimeout(1000);

    // 查找并点击注册链接
    const signUpLink = page.getByRole('button', { name: '创建账户' }).or(
      page.getByRole('button', { name: 'Create Account' })
    );

    if (await signUpLink.isVisible({ timeout: 3000 })) {
      await signUpLink.click();
      await page.waitForTimeout(1000);

      // 验证注册表单显示
      const joinText = page.getByText('加入网站收藏夹').or(page.getByText('Join Website Curator'));
      await expect(joinText).toBeVisible();

      const nameField = page.getByLabel('姓名').or(page.getByLabel(/full name|name/i));
      await expect(nameField).toBeVisible();

      const emailField = page.getByLabel('邮箱地址').or(page.getByLabel('Email Address'));
      await expect(emailField).toBeVisible();

      const passwordField = page.getByLabel('密码').or(page.getByLabel('Password'));
      await expect(passwordField).toBeVisible();

      // 验证注册按钮
      const createAccountButton = page.getByRole('button', { name: '创建账户' }).or(
        page.getByRole('button', { name: /create account|sign up/i })
      );
      await expect(createAccountButton).toBeVisible();
    } else {
      console.log('⚠️ 注册链接不可见，可能需要滚动或等待');
    }
  });

  test('登录表单验证', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    await page.locator('header').getByRole('button', { name: 'Sign In' }).click();
    await page.waitForTimeout(500);

    // 尝试空表单提交
    const submitButton = page.getByRole('button', { name: 'Sign In' }).filter({
      hasNotText: 'Sign In to Save Favorites'
    });
    await submitButton.click();
    await page.waitForTimeout(1000);

    // 验证错误消息（根据实际的错误消息文本）
    const errorMessage = page.getByText(/please fill in all fields/i)
      .or(page.getByText(/email is required/i))
      .or(page.getByText(/all fields are required/i))
      .or(page.locator('[role="alert"]'))
      .or(page.locator('.text-red-500, .text-destructive'));

    if (await errorMessage.isVisible({ timeout: 2000 })) {
      await expect(errorMessage).toBeVisible();
      console.log('✅ 表单验证错误消息显示正常');
    } else {
      console.log('ℹ️ 未检测到表单验证错误消息，可能使用了不同的验证方式');
    }
  });

  test('用户登录流程', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // 点击头部的登录按钮
    await page.locator('header').getByRole('button', { name: 'Sign In' }).click();
    await page.waitForTimeout(500);

    // 填写登录表单
    await page.getByLabel('Email Address').fill(testUser.email);
    await page.getByLabel('Password').fill(testUser.password);

    // 提交表单
    const submitButton = page.getByRole('button', { name: 'Sign In' }).filter({
      hasNotText: 'Sign In to Save Favorites'
    });
    await submitButton.click();

    // 等待登录处理（增加等待时间）
    await page.waitForTimeout(5000);

    // 验证登录结果
    // 查找用户头像（Avatar组件）
    const userAvatar = page.locator('header').locator('[role="button"]').filter({
      has: page.locator('div').filter({ hasText: /^[A-Z]$/ })
    });

    // 查找错误消息
    const errorMessage = page.getByText(/invalid email or password/i)
      .or(page.getByText(/invalid credentials/i))
      .or(page.getByText(/login failed/i))
      .or(page.locator('[role="alert"]').filter({ hasText: /error|failed/i }));

    const isLoggedIn = await userAvatar.isVisible({ timeout: 2000 });
    const hasError = await errorMessage.isVisible({ timeout: 2000 });

    if (isLoggedIn) {
      console.log('✅ 登录成功');
      await expect(userAvatar).toBeVisible();

      // 验证登录后的UI变化
      await expect(page.locator('header').getByRole('button', { name: 'Sign In' })).not.toBeVisible();
    } else if (hasError) {
      console.log('ℹ️ 登录失败 - 可能是测试用户不存在或密码错误');
      await expect(errorMessage).toBeVisible();
    } else {
      console.log('ℹ️ 登录状态不明确，检查是否有其他指示器');

      // 检查是否有其他登录成功的指示器
      const signInButton = page.locator('header').getByRole('button', { name: 'Sign In' });
      const isSignInButtonHidden = !(await signInButton.isVisible({ timeout: 1000 }));

      if (isSignInButtonHidden) {
        console.log('✅ 登录可能成功（登录按钮已隐藏）');
      }
    }
  });

  test('密码可见性切换', async ({ page }) => {
    await page.goto('/');
    await page.locator('header').getByRole('button', { name: 'Sign In' }).click();

    const passwordInput = page.getByLabel('Password');
    await passwordInput.fill('testpassword');

    // 查找密码可见性切换按钮（在密码输入框旁边）
    const passwordContainer = page.locator('div').filter({ hasText: /^Password$/ });
    const toggleButton = passwordContainer.getByRole('button');

    if (await toggleButton.isVisible()) {
      // 验证初始状态是隐藏密码
      await expect(passwordInput).toHaveAttribute('type', 'password');

      // 点击切换按钮
      await toggleButton.click();

      // 验证密码变为可见
      await expect(passwordInput).toHaveAttribute('type', 'text');

      // 再次点击切换回隐藏
      await toggleButton.click();
      await expect(passwordInput).toHaveAttribute('type', 'password');
    }
  });

  test('登录后用户菜单', async ({ page }) => {
    await page.goto('/');

    // 尝试登录
    await page.locator('header').getByRole('button', { name: 'Sign In' }).click();
    await page.getByLabel('Email Address').fill(testUser.email);
    await page.getByLabel('Password').fill(testUser.password);
    await page.getByRole('button', { name: /sign in/i }).last().click();

    // 等待登录处理
    await page.waitForTimeout(3000);

    // 查找用户头像或菜单
    const userMenu = page.locator('[role="button"]').filter({ hasText: /T|U/ }).first();

    if (await userMenu.isVisible()) {
      // 点击用户菜单
      await userMenu.click();

      // 验证菜单选项
      await expect(page.getByText(/my favorites/i).or(page.getByText(/favorites/i))).toBeVisible();
      await expect(page.getByText(/sign out/i).or(page.getByText(/logout/i))).toBeVisible();
    }
  });

  test('登出功能', async ({ page }) => {
    await page.goto('/');

    // 尝试登录
    await page.locator('header').getByRole('button', { name: 'Sign In' }).click();
    await page.getByLabel('Email Address').fill(testUser.email);
    await page.getByLabel('Password').fill(testUser.password);
    await page.getByRole('button', { name: /sign in/i }).last().click();

    await page.waitForTimeout(3000);

    // 如果登录成功，测试登出
    const userMenu = page.locator('[role="button"]').filter({ hasText: /T|U/ }).first();

    if (await userMenu.isVisible()) {
      await userMenu.click();

      // 点击登出
      const signOutButton = page.getByText(/sign out/i).or(page.getByText(/logout/i));
      await signOutButton.click();

      // 验证已登出
      await page.waitForTimeout(1000);
      await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();
    }
  });

  test('未登录用户访问受保护功能', async ({ page }) => {
    await page.goto('/');

    // 尝试点击收藏按钮（如果存在）
    const favoriteButtons = page.locator('button').filter({
      has: page.locator('svg[data-lucide="heart"]')
    });

    if (await favoriteButtons.count() > 0) {
      await favoriteButtons.first().click();

      // 应该提示登录或显示登录对话框
      await page.waitForTimeout(1000);

      // 验证是否显示了登录提示
      const loginPrompt = page.getByText(/请登录/i).or(
        page.getByText(/please log in/i)
      ).or(
        page.getByText(/登录以继续/i)
      ).or(
        page.getByText(/欢迎回来/i)
      );

      if (await loginPrompt.isVisible()) {
        await expect(loginPrompt).toBeVisible();
      }
    }
  });

  test('账户禁用状态检查', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // 尝试使用被禁用的测试账户登录
    const disabledUser = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const signInButton = page.locator('header').getByRole('button', { name: '登录' }).or(
      page.locator('header').getByRole('button', { name: 'Sign In' })
    );
    await signInButton.click();
    await page.waitForTimeout(1000);

    // 填写登录表单
    const emailField = page.getByLabel('邮箱地址').or(page.getByLabel('Email Address'));
    const passwordField = page.getByLabel('密码').or(page.getByLabel('Password'));

    await emailField.fill(disabledUser.email);
    await passwordField.fill(disabledUser.password);

    // 提交表单
    const submitButton = page.getByRole('button', { name: '登录' }).or(
      page.getByRole('button', { name: 'Sign In' })
    ).filter({ hasNotText: '登录以保存收藏' });
    await submitButton.click();
    await page.waitForTimeout(3000);

    // 检查是否显示账户禁用消息
    const disabledMessage = page.getByText(/账户已被禁用/i).or(
      page.getByText(/account.*disabled/i)
    ).or(
      page.getByText(/联系管理员/i)
    );

    if (await disabledMessage.isVisible({ timeout: 5000 })) {
      await expect(disabledMessage).toBeVisible();
      console.log('✅ 账户禁用检查正常');
    } else {
      console.log('ℹ️ 测试账户可能不存在或未被禁用');
    }
  });

  test('管理员登录验证', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    const signInButton = page.locator('header').getByRole('button', { name: '登录' }).or(
      page.locator('header').getByRole('button', { name: 'Sign In' })
    );
    await signInButton.click();
    await page.waitForTimeout(1000);

    // 使用管理员账户登录
    const emailField = page.getByLabel('邮箱地址').or(page.getByLabel('Email Address'));
    const passwordField = page.getByLabel('密码').or(page.getByLabel('Password'));

    await emailField.fill(adminUser.email);
    await passwordField.fill(adminUser.password);

    const submitButton = page.getByRole('button', { name: '登录' }).or(
      page.getByRole('button', { name: 'Sign In' })
    ).filter({ hasNotText: '登录以保存收藏' });
    await submitButton.click();
    await page.waitForTimeout(5000);

    // 检查是否登录成功并有管理员权限
    const userAvatar = page.locator('header').locator('[role="button"]').filter({
      has: page.locator('div').filter({ hasText: /^[A-Z]$/ })
    });

    if (await userAvatar.isVisible({ timeout: 3000 })) {
      await userAvatar.click();
      await page.waitForTimeout(500);

      // 检查是否有管理员菜单项
      const adminMenu = page.getByText('管理面板').or(
        page.getByText('Admin Panel')
      ).or(
        page.getByText('用户管理')
      );

      if (await adminMenu.isVisible({ timeout: 2000 })) {
        await expect(adminMenu).toBeVisible();
        console.log('✅ 管理员登录成功，管理员菜单可见');
      } else {
        console.log('ℹ️ 管理员菜单不可见，可能权限不足');
      }
    } else {
      console.log('⚠️ 管理员登录失败');
    }
  });
});
