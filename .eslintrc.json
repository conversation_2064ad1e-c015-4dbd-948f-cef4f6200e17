{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "off", "react-hooks/exhaustive-deps": "warn", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "warn", "prefer-const": "warn", "no-var": "error", "react/jsx-no-undef": "error"}, "ignorePatterns": ["node_modules/", ".next/", "out/", "build/", "dist/", "*.config.js", "*.config.mjs", "playwright.config.ts"]}