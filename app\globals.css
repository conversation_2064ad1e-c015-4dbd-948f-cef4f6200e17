@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.75rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 142.1 70.6% 45.3%;
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142.4 71.8% 29.2%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html,
  body {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .animate-in {
    animation-fill-mode: both;
  }

  .fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .slide-in-from-left {
    animation: slideInFromLeft 0.3s ease-out;
  }

  .slide-in-from-right {
    animation: slideInFromRight 0.3s ease-out;
  }

  .slide-in-from-top {
    animation: slideInFromTop 0.3s ease-out;
  }

  .slide-in-from-bottom {
    animation: slideInFromBottom 0.3s ease-out;
  }

  .zoom-in-95 {
    animation: zoomIn95 0.2s ease-out;
  }

  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-gradient {
    background-size: 400% 400%;
    animation: gradientShift 6s ease-in-out infinite;
  }

  .animate-gradient-text {
    background-size: 400% 400%;
    animation: gradientShift 6s ease-in-out infinite;
  }

  .animate-rainbow {
    background-size: 400% 400%;
    animation: rainbow 4s ease-in-out infinite;
  }

  .animate-rainbow-text {
    background-size: 400% 400%;
    animation: rainbow 4s ease-in-out infinite;
  }

  .animate-wave-gradient {
    background-size: 400% 100%;
    animation: waveGradient 8s linear infinite;
  }

  .animate-wave-gradient-fast {
    background-size: 300% 100%;
    animation: waveGradient 4s linear infinite;
  }

  .animate-flowing-gradient {
    background-size: 300% 100%;
    animation: flowingGradient 6s ease-in-out infinite;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes slideInFromLeft {
    from {
      opacity: 0;
      transform: translateX(-10px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInFromRight {
    from {
      opacity: 0;
      transform: translateX(10px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInFromTop {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInFromBottom {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes zoomIn95 {
    from {
      opacity: 0;
      transform: scale(0.98);
    }

    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes float {

    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-15px);
    }
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }

    25% {
      background-position: 100% 50%;
    }

    50% {
      background-position: 100% 100%;
    }

    75% {
      background-position: 0% 100%;
    }

    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes rainbow {
    0% {
      background-position: 0% 50%;
    }

    20% {
      background-position: 100% 50%;
    }

    40% {
      background-position: 100% 100%;
    }

    60% {
      background-position: 0% 100%;
    }

    80% {
      background-position: 0% 0%;
    }

    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes waveGradient {
    0% {
      background-position: -100% 0;
    }

    100% {
      background-position: 100% 0;
    }
  }

  @keyframes flowingGradient {
    0% {
      background-position: -200% 0;
    }

    50% {
      background-position: 200% 0;
    }

    100% {
      background-position: -200% 0;
    }
  }
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* 深色模式下的滚动条 */
.dark .custom-scrollbar {
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.8);
}

/* 移除颜色选择器的默认边框 */
input[type="color"] {
  border: none !important;
  outline: none !important;
  background: none !important;
  padding: 0 !important;
}

input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0 !important;
  border: none !important;
}

input[type="color"]::-webkit-color-swatch {
  border: none !important;
  border-radius: 0.375rem !important;
}

input[type="color"]::-moz-color-swatch {
  border: none !important;
  border-radius: 0.375rem !important;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}