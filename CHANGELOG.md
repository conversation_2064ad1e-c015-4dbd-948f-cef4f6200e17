<!--
 * @Date: 2025-06-29 21:10:07
 * @Author: Sube
 * @FilePath: CHANGELOG.md
 * @LastEditTime: 2025-06-30 04:57:15
 * @Description: 
-->
# 更新日志

本文件记录了项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划中
- 批量导入网站功能
- 网站统计和分析
- API 接口文档
- 移动端 PWA 支持

## [1.1.1] - 2025-06-30

### 新增
- 🛡️ **全局错误处理器**: 新增GlobalErrorHandler组件，捕获React hydration错误和未处理的Promise拒绝
- 📡 **网络状态监控**: 实现NetworkMonitor组件，提供实时网络状态监控和用户友好的网络连接提示
- ⚙️ **开发环境配置**: 新增dev-config.ts，支持开发时禁用favicon获取和其他优化选项
- 💾 **Favicon API缓存**: 实现内存缓存机制（5分钟成功缓存，30分钟错误缓存），避免重复请求
- 📖 **开发环境文档**: 添加详细的开发环境优化指南和故障排除文档

### 修复
- 🔄 **Hydration错误修复**:
  - 修复主题切换按钮在服务端和客户端渲染不一致的问题
  - 修复Toaster组件的主题hydration不匹配
  - 修复useIsMobile钩子在hydration时的状态不一致
- �️ **SSR构建错误修复**:
  - 修复NetworkMonitor组件在SSR时访问navigator对象的问题
  - 修复sidebar组件在SSR时访问document.cookie的问题
  - 修复theme-context在SSR时访问localStorage和document的问题
  - 解决构建时'navigator is not defined'错误
- �🎯 **UI组件修复**:
  - 修复button元素缺少type属性的问题
  - 优化管理后台标签页在移动端的水平滚动显示

### 性能优化
- 🖼️ **Favicon优化**:
  - 减少favicon获取重试次数从2次降至1次
  - 简化fallback策略，减少无效网络请求
  - 支持开发环境完全禁用favicon获取
- 🔄 **React Query优化**:
  - 改进错误重试策略，根据错误类型智能重试
  - 优化重试延迟算法，避免过于频繁的请求
- 📝 **错误日志优化**:
  - 减少生产环境的错误日志输出，避免控制台污染
  - 在开发环境提供更详细的调试信息

### 开发体验改进
- 🔧 **开发配置选项**:
  - 支持通过环境变量禁用favicon获取 (`DISABLE_FAVICON=true`)
  - 支持使用模拟favicon (`USE_MOCK_FAVICON=true`)
  - 支持控制错误日志显示 (`SHOW_NETWORK_ERRORS=false`)
- 🛠️ **调试工具**:
  - 新增开发环境日志工具 (devLog)
  - 提供favicon获取状态的详细监控
  - 添加性能警告和网络错误追踪

### 技术改进
- 📱 **移动端优化**: 改进管理后台标签页的响应式设计和水平滚动体验
- 🎨 **UI一致性**: 统一标签页样式、间距和交互效果
- 🔒 **错误处理**: 全面的错误捕获和恢复机制
- ⚡ **缓存策略**: 智能的favicon缓存和网络请求优化
- 🏗️ **SSR兼容性**: 修复服务端渲染时的浏览器API访问问题，确保构建成功

## [1.1.0] - 2025-06-30

### 新增
- 🖼️ 动态favicon API服务，自动获取网站图标
- 📚 完整的项目文档体系（ARCHITECTURE.md）
- 🖼️ 项目截图集合，展示各功能界面

### 改进
- 📱 优化管理页面标签页导航，支持移动端水平滑动
- 🔧 修复所有对话框在移动端的适配问题
- 📏 统一对话框尺寸和圆角效果，提升用户体验
- 🎨 优化README文档徽章布局，改为水平排列
- ⚡ 增强网站浏览和收藏页面组件性能

### 修复
- 📱 解决移动端标签页显示不全的问题
- 🔲 修复对话框在小屏幕设备上过大的问题
- 📐 统一所有Dialog和AlertDialog的响应式设计
- 🎯 改进组件的错误处理和用户反馈

### 技术改进
- 🎨 添加scrollbar-hide样式类，隐藏滚动条保持美观
- 📱 优化响应式布局，桌面端和移动端体验一致
- 🔄 改进组件的性能和交互体验
- 📝 完善项目文档和可视化展示

## [1.0.0] - 2025-06-29

### 新增
- 🎉 项目初始发布
- 🔐 完整的用户认证系统（基于 Supabase Auth）
- 👥 三级用户权限管理（user, admin, super_admin）
- 📝 网站提交和审核功能
- 🏷️ 分类和标签管理系统
- ⭐ 用户收藏功能
- 🔍 网站搜索功能
- 🎨 明暗主题切换
- 📱 响应式设计
- ⚙️ 系统设置管理
- 🧪 完整的测试套件（Playwright）

### 技术特性
- Next.js 15 + React 19
- Supabase 数据库和认证
- Tailwind CSS + shadcn/ui
- React Query 状态管理
- TypeScript 类型安全
- Row Level Security (RLS)
- 性能优化（乐观更新）

### 安全特性
- 数据库 RLS 策略
- 用户权限控制
- 输入验证和清理
- 安全的会话管理
- 环境变量保护

### 用户界面
- 现代化设计
- 流畅的动画效果
- 直观的用户体验
- 无障碍访问支持
- 移动端优化

### 管理功能
- 用户管理面板
- 网站审核系统
- 分类管理
- 系统设置
- 数据统计

---

## 版本说明

### 版本号规则
- **主版本号**：不兼容的 API 修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 变更类型
- **新增** - 新功能
- **变更** - 对现有功能的变更
- **弃用** - 即将移除的功能
- **移除** - 已移除的功能
- **修复** - 问题修复
- **安全** - 安全相关的修复

### 贡献指南
如需贡献代码或报告问题，请查看 [CONTRIBUTING.md](CONTRIBUTING.md)。

### 支持
如有问题或需要帮助，请：
- 查看 [Issues](https://github.com/Sube3494/website-curator/issues)
- 发送邮件至 <EMAIL>
