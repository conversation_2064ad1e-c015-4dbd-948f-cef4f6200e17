# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
/playwright/videos/
/playwright/screenshots/
/playwright/downloads/
*.spec.js.map
*.test.js.map
test-results.xml
junit.xml

# next.js
/.next/
/out/

# production
/build
/dist
/issues

# misc
.DS_Store
*.pem
.vscode/
.idea/

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
lerna-debug.log*

# local env files
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# turbo
.turbo

# Supabase
.branches
.temp
.import
.migration

#memory bank
memory_bank.md