name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  lint-and-typecheck:
    name: 代码检查
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 安装 pnpm
      uses: pnpm/action-setup@v2
      with:
        version: ${{ env.PNPM_VERSION }}

    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'pnpm'

    - name: 安装依赖
      run: pnpm install --no-frozen-lockfile

    - name: 代码规范检查
      run: pnpm lint

    - name: TypeScript 类型检查
      run: pnpm run build
      env:
        NEXT_PUBLIC_SUPABASE_URL: 'https://placeholder.supabase.co'
        NEXT_PUBLIC_SUPABASE_ANON_KEY: 'placeholder-key'



  security:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: lint-and-typecheck
    if: ${{ vars.ENABLE_SECURITY_SCAN == 'true' }}

    permissions:
      security-events: write
      actions: read
      contents: read

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 运行 Trivy 漏洞扫描
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: 上传扫描结果到 GitHub Security
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  build:
    name: 构建应用
    runs-on: ubuntu-latest
    needs: lint-and-typecheck

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 安装 pnpm
      uses: pnpm/action-setup@v2
      with:
        version: ${{ env.PNPM_VERSION }}

    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'pnpm'

    - name: 安装依赖
      run: pnpm install --no-frozen-lockfile

    - name: 构建应用
      run: pnpm build
      env:
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co' }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key' }}

    - name: 上传构建产物
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: .next/
        retention-days: 7

  deploy:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push' && vars.ENABLE_AUTO_DEPLOY == 'true'

    environment:
      name: production
      url: ${{ steps.deploy.outputs.url }}

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 下载构建产物
      uses: actions/download-artifact@v4
      with:
        name: build-files
        path: .next/

    - name: 部署到 Vercel
      id: deploy
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        vercel-args: '--prod'
        working-directory: ./

    - name: 部署成功通知
      if: success()
      run: |
        echo "🚀 部署成功！"
        echo "📱 应用地址: ${{ steps.deploy.outputs.preview-url }}"
