# 🔒 安全政策

## 支持的版本

我们为以下版本提供安全更新：

| 版本 | 支持状态 |
| --- | --- |
| 1.x.x | ✅ |
| < 1.0 | ❌ |

## 报告安全漏洞

如果您发现了安全漏洞，请**不要**通过公开的 GitHub Issues 报告。

### 报告方式

请通过以下方式私下报告安全问题：

- **邮箱**: <EMAIL>
- **主题**: [SECURITY] Website Curator 安全漏洞报告

### 报告内容

请在报告中包含以下信息：

1. **漏洞描述** - 详细描述发现的安全问题
2. **影响范围** - 说明漏洞可能造成的影响
3. **复现步骤** - 提供详细的复现步骤
4. **环境信息** - 包括版本号、浏览器、操作系统等
5. **修复建议** - 如果有修复建议请一并提供

### 响应时间

- **确认收到**: 24 小时内
- **初步评估**: 72 小时内
- **详细分析**: 7 天内
- **修复发布**: 根据严重程度，1-30 天内

## 安全最佳实践

### 环境变量安全

- ✅ 使用 `.env.local` 存储敏感信息
- ✅ 不要将 `.env*` 文件提交到版本控制
- ✅ 仅在必要时使用 `NEXT_PUBLIC_` 前缀
- ❌ 不要在客户端代码中硬编码敏感信息

### 数据库安全

- ✅ 启用 Row Level Security (RLS)
- ✅ 使用最小权限原则
- ✅ 定期更新数据库密码
- ✅ 启用数据库审计日志
- ❌ 不要在客户端暴露服务密钥

### 认证安全

- ✅ 使用 Supabase Auth 的安全功能
- ✅ 实施适当的会话管理
- ✅ 启用多因素认证（如果可用）
- ✅ 定期审查用户权限
- ❌ 不要在本地存储敏感的认证信息

### 输入验证

- ✅ 使用 Zod 进行数据验证
- ✅ 对所有用户输入进行清理
- ✅ 实施 CSRF 保护
- ✅ 验证文件上传类型和大小
- ❌ 不要信任客户端验证

### 网络安全

- ✅ 使用 HTTPS
- ✅ 设置适当的 CORS 策略
- ✅ 实施速率限制
- ✅ 使用安全的 HTTP 头
- ❌ 不要暴露敏感的错误信息

## 安全配置检查清单

### 生产环境部署前

- [ ] 所有环境变量已正确配置
- [ ] 数据库 RLS 策略已启用
- [ ] HTTPS 已配置
- [ ] 安全头已设置
- [ ] 错误处理不暴露敏感信息
- [ ] 日志记录已配置
- [ ] 备份策略已实施
- [ ] 监控和告警已设置

### 定期安全检查

- [ ] 依赖包安全扫描
- [ ] 数据库权限审查
- [ ] 用户权限审查
- [ ] 日志分析
- [ ] 性能监控
- [ ] 备份测试

## 已知安全考虑

### 当前实施的安全措施

1. **Row Level Security (RLS)**
   - 所有数据表都启用了 RLS
   - 用户只能访问授权的数据

2. **用户权限管理**
   - 三级权限系统：user, admin, super_admin
   - 权限最小化原则

3. **输入验证**
   - 使用 Zod 进行数据验证
   - 前后端双重验证

4. **会话管理**
   - 使用 Supabase Auth 的安全会话管理
   - 自动令牌刷新

### 潜在风险点

1. **客户端状态管理**
   - 敏感数据不应在客户端长期存储
   - 定期清理本地存储

2. **文件上传**
   - 当前版本不支持文件上传
   - 未来版本需要实施严格的文件验证

3. **API 速率限制**
   - 建议在生产环境中实施 API 速率限制

## 安全更新

我们会定期发布安全更新。请关注：

- GitHub Releases
- Security Advisories
- 邮件通知（如果您订阅了）

## 联系信息

如有安全相关问题，请联系：

- **安全邮箱**: <EMAIL>
- **GitHub**: [@Sube3494](https://github.com/Sube3494)

---

感谢您帮助保持 Website Curator 的安全！
