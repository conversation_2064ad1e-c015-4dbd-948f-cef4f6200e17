## 📋 变更描述
简洁明了地描述此 PR 的变更内容。

## 🔗 相关 Issue
修复 #(issue 编号)

## 📝 变更类型
请删除不相关的选项：

- [ ] Bug 修复（不破坏现有功能的非破坏性变更）
- [ ] 新功能（添加功能的非破坏性变更）
- [ ] 破坏性变更（会导致现有功能无法正常工作的修复或功能）
- [ ] 文档更新（仅文档变更）
- [ ] 性能改进
- [ ] 代码重构
- [ ] 测试相关
- [ ] 构建/CI 相关

## 🧪 测试
请描述您运行的测试来验证您的更改。请提供重现的说明。

- [ ] 单元测试
- [ ] 集成测试
- [ ] E2E 测试
- [ ] 手动测试

**测试配置**:
* 操作系统：
* 浏览器：
* Node.js 版本：

## ✅ 检查清单
- [ ] 我的代码遵循此项目的样式指南
- [ ] 我已经对自己的代码进行了自我审查
- [ ] 我已经对代码进行了注释，特别是在难以理解的区域
- [ ] 我已经对我的更改进行了相应的文档更改
- [ ] 我的更改不会产生新的警告
- [ ] 我已经添加了证明我的修复有效或我的功能有效的测试
- [ ] 新的和现有的单元测试在我的更改下都通过了本地测试
- [ ] 任何依赖的更改都已合并并发布到下游模块

## 📸 截图（如果适用）
添加截图来帮助解释您的更改。

## 📝 附加说明
在此处添加有关此 PR 的任何其他说明。

## 🔍 审查者注意事项
请特别关注以下方面：
- [ ] 安全性
- [ ] 性能
- [ ] 可访问性
- [ ] 移动端兼容性
